[package]
edition = "2021"
name    = "esp32-manifold"
version = "0.1.0"

[[bin]]
name = "esp32-manifold"
path = "./src/bin/main.rs"
test = false

[[test]]
harness = false
name    = "hello_test"

[lib]
test = false

[dependencies]
defmt = "1.0.1"
esp-bootloader-esp-idf = "0.1.0"
esp-hal = { version = "=1.0.0-beta.1", features = [
  "defmt",
  "esp32",
  "unstable",
] }

embassy-net = { version = "0.7.0", features = [
  "defmt",
  "dhcpv4",
  "medium-ethernet",
  "tcp",
  "udp",
] }
embedded-io = { version = "0.6.1", features = ["defmt-03"] }
embedded-io-async = { version = "0.6.1", features = ["defmt-03"] }
esp-alloc = { version = "0.8.0", features = ["defmt"] }
panic-rtt-target = { version = "0.2.0", features = ["defmt"] }
rtt-target = { version = "0.6.1", features = ["defmt"] }
# for more networking protocol support see https://crates.io/crates/edge-net
critical-section = "1.2.0"
embassy-executor = { version = "0.7.0", features = [
  "defmt",
  "task-arena-size-20480",
] }
embassy-time = { version = "0.4.0", features = ["defmt"] }
esp-hal-embassy = { version = "0.8.1", features = ["defmt", "esp32"] }
esp-wifi = { version = "0.14.1", features = [
  "builtin-scheduler",
  "defmt",
  "esp-alloc",
  "esp32",
  "smoltcp",
  "wifi",
] }
smoltcp = { version = "0.12.0", default-features = false, features = [
  "defmt",
  "medium-ethernet",
  "multicast",
  "proto-dhcpv4",
  "proto-dns",
  "proto-ipv4",
  "socket-dns",
  "socket-icmp",
  "socket-raw",
  "socket-tcp",
  "socket-udp",
] }
static_cell = { version = "2.1.0", features = ["nightly"] }
wyhash = "0.6.0"
hashbrown = "0.15.4"

[dev-dependencies]
embedded-test = { version = "0.6.0", features = [
  "defmt",
  "embassy",
  "external-executor",
  "xtensa-semihosting",
] }

[profile.dev]
# Rust debug is too slow.
# For debug builds always builds with some optimization
opt-level = "s"

[profile.release]
codegen-units    = 1     # LLVM can perform better optimizations using a single thread
debug            = 2
debug-assertions = false
incremental      = false
lto              = 'fat'
opt-level        = 's'
overflow-checks  = false
