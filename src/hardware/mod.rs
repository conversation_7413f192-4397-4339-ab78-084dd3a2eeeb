//! # Hardware Abstraction Module
//!
//! This module provides hardware abstraction for ESP32 peripherals.
//! It handles initialization and configuration of all hardware components.

pub mod peripherals;
pub mod timers;

use defmt::info;
use esp_hal::rng::Rng;

use crate::app::config::Config;
use crate::app::error::{AppError, HardwareError, Result};

pub use peripherals::PeripheralManager;
pub use timers::TimerManager;

/// Main hardware abstraction structure
pub struct Hardware {
    peripherals: PeripheralManager,
    timers: TimerManager,
    rng: Option<Rng>,
}

impl Hardware {
    /// Initialize hardware with the given configuration
    pub async fn new(config: &Config) -> Result<Self> {
        info!("Initializing hardware...");

        // Initialize ESP32 with custom configuration
        let esp_config = esp_hal::Config::default().with_cpu_clock(config.hardware.cpu_clock);

        let peripherals = esp_hal::init(esp_config);

        // Note: Heap allocator must be initialized with a constant size
        // This is done in the main function or at module level
        info!("Using heap size: {} bytes", config.hardware.heap_size);

        // Initialize peripheral manager
        let peripheral_manager = PeripheralManager::new(peripherals)?;

        // Initialize timer manager
        let timer_manager = TimerManager::new(&peripheral_manager)?;

        // Initialize Embassy
        timer_manager.init_embassy()?;

        // Initialize RNG
        let rng = peripheral_manager.init_rng()?;

        info!("Hardware initialization complete");

        Ok(Self {
            peripherals: peripheral_manager,
            timers: timer_manager,
            rng: Some(rng),
        })
    }

    /// Get reference to peripheral manager
    pub fn peripherals(&self) -> &PeripheralManager {
        &self.peripherals
    }

    /// Get mutable reference to peripheral manager
    pub fn peripherals_mut(&mut self) -> &mut PeripheralManager {
        &mut self.peripherals
    }

    /// Get reference to timer manager
    pub fn timers(&self) -> &TimerManager {
        &self.timers
    }

    /// Get reference to RNG
    pub fn rng(&self) -> Option<&Rng> {
        self.rng.as_ref()
    }

    /// Get mutable reference to RNG
    pub fn rng_mut(&mut self) -> Option<&mut Rng> {
        self.rng.as_mut()
    }

    /// Initialize RTT for debugging
    pub fn init_rtt() -> Result<()> {
        rtt_target::rtt_init_defmt!();
        info!("RTT initialized for debugging");
        Ok(())
    }

    /// Get system information
    pub fn system_info(&self) -> SystemInfo {
        SystemInfo {
            cpu_frequency: self.peripherals.cpu_frequency(),
            heap_size: self.peripherals.heap_size(),
            free_heap: self.peripherals.free_heap(),
            uptime_ms: self.timers.uptime_ms(),
        }
    }

    /// Perform hardware self-test
    pub async fn self_test(&mut self) -> Result<SelfTestResult> {
        info!("Performing hardware self-test...");

        let mut result = SelfTestResult::default();

        // Test RNG
        if let Some(rng) = &mut self.rng {
            result.rng_test = Self::test_rng(rng);
        }

        // Test timers
        result.timer_test = self.timers.self_test().await;

        // Test peripherals
        result.peripheral_test = self.peripherals.self_test();

        info!("Hardware self-test complete: {:?}", result);
        Ok(result)
    }

    /// Test RNG functionality
    fn test_rng(rng: &mut Rng) -> bool {
        // Generate a few random numbers and check they're different
        let r1 = rng.random();
        let r2 = rng.random();
        let r3 = rng.random();

        // Very basic test - just check they're not all the same
        !(r1 == r2 && r2 == r3)
    }

    /// Shutdown hardware gracefully
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down hardware...");

        // Shutdown peripherals
        self.peripherals.shutdown()?;

        // Note: We don't shutdown timers as they're needed for Embassy

        info!("Hardware shutdown complete");
        Ok(())
    }
}

/// System information structure
#[derive(Debug)]
pub struct SystemInfo {
    pub cpu_frequency: u32,
    pub heap_size: usize,
    pub free_heap: usize,
    pub uptime_ms: u64,
}

/// Hardware self-test results
#[derive(Debug, Default, defmt::Format)]
pub struct SelfTestResult {
    pub rng_test: bool,
    pub timer_test: bool,
    pub peripheral_test: bool,
}

impl SelfTestResult {
    /// Check if all tests passed
    pub fn all_passed(&self) -> bool {
        self.rng_test && self.timer_test && self.peripheral_test
    }

    /// Get failed test count
    pub fn failed_count(&self) -> u8 {
        let mut count = 0;

        if !self.rng_test {
            count += 1;
        }
        if !self.timer_test {
            count += 1;
        }
        if !self.peripheral_test {
            count += 1;
        }

        count
    }
}

/// Hardware configuration validation
pub fn validate_hardware_config(config: &Config) -> Result<()> {
    if config.hardware.heap_size < 16 * 1024 {
        return Err(AppError::Hardware(HardwareError::MemoryAllocation(
            "Heap size too small (minimum 16KB)",
        )));
    }

    if config.hardware.heap_size > 512 * 1024 {
        return Err(AppError::Hardware(HardwareError::MemoryAllocation(
            "Heap size too large (maximum 512KB)",
        )));
    }

    Ok(())
}
