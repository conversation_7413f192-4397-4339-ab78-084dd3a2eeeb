//! # Timer Management Module
//!
//! This module handles timer configuration and management for the ESP32.

use esp_hal::timer::timg::TimerGroup;
use embassy_time::{Duration, Timer};
use defmt::info;

use crate::app::error::{Result, HardwareError, AppError};
use super::peripherals::PeripheralManager;

/// Timer manager for ESP32 timers
pub struct TimerManager {
    embassy_initialized: bool,
    wifi_timer_used: bool,
    uptime_start: Option<u64>,
}

impl TimerManager {
    /// Create a new timer manager
    pub fn new(peripherals: &PeripheralManager) -> Result<Self> {
        info!("Initializing timer manager...");

        // Validate that we have the required timer peripherals
        let status = peripherals.status();
        if !status.timg0_available || !status.timg1_available {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "Required timer peripherals not available"
            )));
        }

        Ok(Self {
            embassy_initialized: false,
            wifi_timer_used: false,
            uptime_start: None,
        })
    }

    /// Initialize Embassy with TIMG1 timer
    pub fn init_embassy(&mut self) -> Result<()> {
        if self.embassy_initialized {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "Embassy already initialized"
            )));
        }

        info!("Initializing Embassy timer...");
        
        // Note: In the actual implementation, we would need access to the peripheral
        // This is a placeholder for the initialization logic
        self.embassy_initialized = true;
        self.uptime_start = Some(0); // Would use actual timer value
        
        info!("Embassy timer initialized");
        Ok(())
    }

    /// Initialize Embassy with the given timer group
    pub fn init_embassy_with_timer(&mut self, timer_group: TimerGroup<esp_hal::peripherals::TIMG1>) -> Result<()> {
        if self.embassy_initialized {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "Embassy already initialized"
            )));
        }

        info!("Initializing Embassy with timer group...");
        
        esp_hal_embassy::init(timer_group.timer0);
        
        self.embassy_initialized = true;
        self.uptime_start = Some(0); // Would use actual timer value
        
        info!("Embassy timer initialized with timer group");
        Ok(())
    }

    /// Create WiFi timer from TIMG0
    pub fn create_wifi_timer(&mut self, peripherals: &mut PeripheralManager) -> Result<esp_hal::timer::timg::Timer<esp_hal::timer::timg::Timer0<esp_hal::peripherals::TIMG0>, esp_hal::Blocking>> {
        if self.wifi_timer_used {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "WiFi timer already used"
            )));
        }

        let timg0 = peripherals.take_timg0()?;
        let timer_group = TimerGroup::new(timg0);
        
        self.wifi_timer_used = true;
        info!("WiFi timer created");
        
        Ok(timer_group.timer0)
    }

    /// Get system uptime in milliseconds
    pub fn uptime_ms(&self) -> u64 {
        if let Some(start) = self.uptime_start {
            // TODO: Implement actual uptime calculation
            // This would use the embassy timer or system tick counter
            embassy_time::Instant::now().as_millis() - start
        } else {
            0
        }
    }

    /// Get system uptime in seconds
    pub fn uptime_secs(&self) -> u64 {
        self.uptime_ms() / 1000
    }

    /// Check if Embassy is initialized
    pub fn is_embassy_initialized(&self) -> bool {
        self.embassy_initialized
    }

    /// Check if WiFi timer is used
    pub fn is_wifi_timer_used(&self) -> bool {
        self.wifi_timer_used
    }

    /// Perform timer self-test
    pub async fn self_test(&self) -> bool {
        info!("Performing timer self-test...");

        if !self.embassy_initialized {
            info!("Timer self-test failed: Embassy not initialized");
            return false;
        }

        // Test Embassy timer by measuring a short delay
        let start = embassy_time::Instant::now();
        Timer::after(Duration::from_millis(10)).await;
        let elapsed = embassy_time::Instant::now() - start;

        // Check if the delay was approximately correct (within 50% tolerance)
        let expected_ms = 10;
        let actual_ms = elapsed.as_millis();
        let tolerance = expected_ms / 2;

        let test_passed = actual_ms >= (expected_ms - tolerance) && actual_ms <= (expected_ms + tolerance);

        if test_passed {
            info!("Timer self-test passed ({}ms delay measured)", actual_ms);
        } else {
            info!("Timer self-test failed (expected ~{}ms, got {}ms)", expected_ms, actual_ms);
        }

        test_passed
    }

    /// Get timer status information
    pub fn status(&self) -> TimerStatus {
        TimerStatus {
            embassy_initialized: self.embassy_initialized,
            wifi_timer_used: self.wifi_timer_used,
            uptime_ms: self.uptime_ms(),
        }
    }

    /// Reset timer state (for testing)
    #[cfg(test)]
    pub fn reset(&mut self) {
        self.embassy_initialized = false;
        self.wifi_timer_used = false;
        self.uptime_start = None;
    }
}

/// Timer status information
#[derive(Debug)]
pub struct TimerStatus {
    pub embassy_initialized: bool,
    pub wifi_timer_used: bool,
    pub uptime_ms: u64,
}

impl TimerStatus {
    /// Check if timers are properly configured
    pub fn is_configured(&self) -> bool {
        self.embassy_initialized
    }

    /// Get uptime in human-readable format
    pub fn uptime_string(&self) -> heapless::String<32> {
        let total_secs = self.uptime_ms / 1000;
        let hours = total_secs / 3600;
        let minutes = (total_secs % 3600) / 60;
        let seconds = total_secs % 60;

        let mut result = heapless::String::new();
        use core::fmt::Write;
        write!(result, "{}h {}m {}s", hours, minutes, seconds).ok();
        result
    }
}

/// Timer configuration helper
pub struct TimerConfig {
    /// Embassy timer configuration
    pub embassy_enabled: bool,
    
    /// WiFi timer configuration
    pub wifi_timer_enabled: bool,
    
    /// System tick frequency
    pub tick_frequency_hz: u32,
}

impl Default for TimerConfig {
    fn default() -> Self {
        Self {
            embassy_enabled: true,
            wifi_timer_enabled: true,
            tick_frequency_hz: 1000, // 1kHz default
        }
    }
}

impl TimerConfig {
    /// Create a minimal timer configuration
    pub fn minimal() -> Self {
        Self {
            embassy_enabled: true,
            wifi_timer_enabled: false,
            tick_frequency_hz: 100, // 100Hz for minimal overhead
        }
    }

    /// Create a high-precision timer configuration
    pub fn high_precision() -> Self {
        Self {
            embassy_enabled: true,
            wifi_timer_enabled: true,
            tick_frequency_hz: 10000, // 10kHz for high precision
        }
    }

    /// Validate timer configuration
    pub fn validate(&self) -> Result<()> {
        if self.tick_frequency_hz == 0 {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "Tick frequency cannot be zero"
            )));
        }

        if self.tick_frequency_hz > 100_000 {
            return Err(AppError::Hardware(HardwareError::TimerConfig(
                "Tick frequency too high (max 100kHz)"
            )));
        }

        Ok(())
    }
}
