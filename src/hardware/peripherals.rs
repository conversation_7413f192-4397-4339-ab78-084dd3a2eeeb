//! # Peripheral Management Module
//!
//! This module handles ESP32 peripheral initialization and management.

use esp_hal::{Peripherals, rng::Rng};
use defmt::info;

use crate::app::error::{Result, HardwareError, AppError};

/// Peripheral manager for ESP32 hardware
pub struct PeripheralManager {
    // Store peripheral handles that we need to keep alive
    radio_clk: esp_hal::peripherals::RADIO_CLK,
    wifi: esp_hal::peripherals::WIFI,
    timg0: Option<esp_hal::peripherals::TIMG0>,
    timg1: Option<esp_hal::peripherals::TIMG1>,
    rng_peripheral: Option<esp_hal::peripherals::RNG>,
    
    // System information
    cpu_freq: u32,
    heap_size: usize,
}

impl PeripheralManager {
    /// Create a new peripheral manager
    pub fn new(peripherals: Peripherals) -> Result<Self> {
        info!("Initializing peripheral manager...");

        // Extract the peripherals we need
        let Peripherals {
            RADIO_CLK: radio_clk,
            WIFI: wifi,
            TIMG0: timg0,
            TIMG1: timg1,
            RNG: rng,
            ..
        } = peripherals;

        let manager = Self {
            radio_clk,
            wifi,
            timg0: Some(timg0),
            timg1: Some(timg1),
            rng_peripheral: Some(rng),
            cpu_freq: 240_000_000, // Default ESP32 max frequency
            heap_size: 64 * 1024,  // Will be updated when heap is initialized
        };

        info!("Peripheral manager initialized");
        Ok(manager)
    }

    /// Take the TIMG0 peripheral (for WiFi timer)
    pub fn take_timg0(&mut self) -> Result<esp_hal::peripherals::TIMG0> {
        self.timg0.take().ok_or_else(|| {
            AppError::Hardware(HardwareError::PeripheralInit("TIMG0 already taken"))
        })
    }

    /// Take the TIMG1 peripheral (for Embassy timer)
    pub fn take_timg1(&mut self) -> Result<esp_hal::peripherals::TIMG1> {
        self.timg1.take().ok_or_else(|| {
            AppError::Hardware(HardwareError::PeripheralInit("TIMG1 already taken"))
        })
    }

    /// Take the RNG peripheral
    pub fn take_rng(&mut self) -> Result<esp_hal::peripherals::RNG> {
        self.rng_peripheral.take().ok_or_else(|| {
            AppError::Hardware(HardwareError::PeripheralInit("RNG already taken"))
        })
    }

    /// Get reference to RADIO_CLK peripheral
    pub fn radio_clk(&self) -> &esp_hal::peripherals::RADIO_CLK {
        &self.radio_clk
    }

    /// Take the RADIO_CLK peripheral
    pub fn take_radio_clk(self) -> esp_hal::peripherals::RADIO_CLK {
        self.radio_clk
    }

    /// Get reference to WiFi peripheral
    pub fn wifi(&self) -> &esp_hal::peripherals::WIFI {
        &self.wifi
    }

    /// Take the WiFi peripheral
    pub fn take_wifi(self) -> esp_hal::peripherals::WIFI {
        self.wifi
    }

    /// Initialize RNG and return it
    pub fn init_rng(&mut self) -> Result<Rng> {
        let rng_peripheral = self.take_rng()?;
        let rng = Rng::new(rng_peripheral);
        info!("RNG initialized");
        Ok(rng)
    }

    /// Get CPU frequency
    pub fn cpu_frequency(&self) -> u32 {
        self.cpu_freq
    }

    /// Get configured heap size
    pub fn heap_size(&self) -> usize {
        self.heap_size
    }

    /// Get free heap memory (placeholder - would need actual implementation)
    pub fn free_heap(&self) -> usize {
        // TODO: Implement actual free heap calculation
        // This would require integration with the allocator
        self.heap_size / 2 // Placeholder
    }

    /// Update heap size information
    pub fn set_heap_size(&mut self, size: usize) {
        self.heap_size = size;
    }

    /// Perform peripheral self-test
    pub fn self_test(&self) -> bool {
        // Basic test - check that we have the required peripherals
        // In a real implementation, this could test peripheral functionality
        
        info!("Performing peripheral self-test...");
        
        // Check if critical peripherals are available
        let has_radio = true; // We always have RADIO_CLK
        let has_wifi = true;  // We always have WIFI
        
        let result = has_radio && has_wifi;
        
        if result {
            info!("Peripheral self-test passed");
        } else {
            info!("Peripheral self-test failed");
        }
        
        result
    }

    /// Get peripheral status information
    pub fn status(&self) -> PeripheralStatus {
        PeripheralStatus {
            timg0_available: self.timg0.is_some(),
            timg1_available: self.timg1.is_some(),
            rng_available: self.rng_peripheral.is_some(),
            radio_clk_available: true, // Always available until taken
            wifi_available: true,      // Always available until taken
        }
    }

    /// Shutdown peripherals gracefully
    pub fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down peripherals...");
        
        // Reset peripheral state
        // Note: In embedded systems, we typically don't "shutdown" peripherals
        // but rather put them in low-power states or reset them
        
        info!("Peripheral shutdown complete");
        Ok(())
    }
}

/// Peripheral availability status
#[derive(Debug)]
pub struct PeripheralStatus {
    pub timg0_available: bool,
    pub timg1_available: bool,
    pub rng_available: bool,
    pub radio_clk_available: bool,
    pub wifi_available: bool,
}

impl PeripheralStatus {
    /// Check if all peripherals are available
    pub fn all_available(&self) -> bool {
        self.timg0_available
            && self.timg1_available
            && self.rng_available
            && self.radio_clk_available
            && self.wifi_available
    }

    /// Get list of unavailable peripherals
    pub fn unavailable_peripherals(&self) -> Vec<&'static str> {
        let mut unavailable = Vec::new();
        
        if !self.timg0_available {
            unavailable.push("TIMG0");
        }
        if !self.timg1_available {
            unavailable.push("TIMG1");
        }
        if !self.rng_available {
            unavailable.push("RNG");
        }
        if !self.radio_clk_available {
            unavailable.push("RADIO_CLK");
        }
        if !self.wifi_available {
            unavailable.push("WIFI");
        }
        
        unavailable
    }
}

/// GPIO pin configuration helper
pub struct GpioConfig {
    // TODO: Add GPIO configuration helpers
    // This could include pin mapping, configuration presets, etc.
}

impl GpioConfig {
    /// Get default valve control pins
    pub fn default_valve_pins() -> &'static [u8] {
        // Common GPIO pins used for valve control
        &[2, 4, 5, 18, 19, 21, 22, 23]
    }

    /// Get default sensor input pins
    pub fn default_sensor_pins() -> &'static [u8] {
        // Common GPIO pins used for sensor inputs
        &[32, 33, 34, 35, 36, 39]
    }

    /// Validate pin configuration
    pub fn validate_pin(pin: u8) -> Result<()> {
        // ESP32 GPIO validation
        match pin {
            0..=39 => {
                // Check for input-only pins
                if matches!(pin, 34 | 35 | 36 | 37 | 38 | 39) {
                    // These are input-only pins, can't be used for output
                    return Err(AppError::Hardware(HardwareError::GpioConfig(
                        "Pin is input-only"
                    )));
                }
                Ok(())
            }
            _ => Err(AppError::Hardware(HardwareError::GpioConfig(
                "Invalid GPIO pin number"
            ))),
        }
    }
}
