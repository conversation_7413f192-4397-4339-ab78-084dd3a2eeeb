#![no_std]
#![no_main]
#![deny(
    clippy::mem_forget,
    reason = "mem::forget is generally not safe to do with esp_hal types, especially those \
    holding buffers for the duration of a data transfer."
)]

use defmt::{error, info};
use embassy_executor::Spawner;
use esp32_manifold::{app::Config, App};
use panic_rtt_target as _;

extern crate alloc;

// This creates a default app-descriptor required by the esp-idf bootloader.
// For more information see: <https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-reference/system/app_image_format.html#application-description>
esp_bootloader_esp_idf::esp_app_desc!();

#[esp_hal_embassy::main]
async fn main(spawner: Spawner) {
    info!("ESP32 Manifold starting...");

    // Create application configuration
    let config = Config::development(); // Use development config for better debugging

    // Create and initialize the application
    let mut app = App::builder().with_config(config).build();

    // Initialize the application
    match app.initialize(spawner).await {
        Ok(()) => {
            info!("Application initialized successfully");

            // Run the main application loop
            if let Err(e) = app.run().await {
                error!("Application error: {:?}", e);

                // Attempt graceful shutdown
                if let Err(shutdown_err) = app.shutdown().await {
                    error!("Shutdown error: {:?}", shutdown_err);
                }
            }
        }
        Err(e) => {
            error!("Failed to initialize application: {:?}", e);

            // For critical errors, we might want to restart or enter safe mode
            match e.severity() {
                esp32_manifold::app::error::ErrorSeverity::Critical => {
                    error!("Critical error - system cannot continue");
                    // In a real system, this might trigger a restart
                    panic!("Critical initialization failure");
                }
                _ => {
                    error!("Non-critical error - attempting recovery");
                    // Could implement recovery logic here
                }
            }
        }
    }
}
