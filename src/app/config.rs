//! # Configuration Module
//!
//! This module provides centralized configuration management for the application.
//! All configurable parameters are defined here with sensible defaults.

use esp_hal::clock::CpuClock;

/// Application configuration structure
#[derive(Debug, <PERSON>lone)]
pub struct Config {
    /// Hardware configuration
    pub hardware: HardwareConfig,
    
    /// Network configuration
    pub network: NetworkConfig,
    
    /// Application behavior configuration
    pub app: AppConfig,
    
    /// Legacy fields for backward compatibility
    pub wifi_enabled: bool,
    pub debug_enabled: bool,
    pub loop_interval_ms: u64,
}

/// Hardware-specific configuration
#[derive(Debug, Clone)]
pub struct HardwareConfig {
    /// CPU clock frequency
    pub cpu_clock: CpuClock,
    
    /// Heap size in bytes
    pub heap_size: usize,
    
    /// Enable hardware watchdog
    pub watchdog_enabled: bool,
    
    /// RNG seed (None for hardware random)
    pub rng_seed: Option<u64>,
}

/// Network configuration
#[derive(Debug, Clone)]
pub struct NetworkConfig {
    /// Enable WiFi functionality
    pub wifi_enabled: bool,
    
    /// WiFi SSID (if connecting to existing network)
    pub wifi_ssid: Option<&'static str>,
    
    /// WiFi password (if connecting to existing network)
    pub wifi_password: Option<&'static str>,
    
    /// Enable access point mode
    pub ap_mode: bool,
    
    /// Access point SSID
    pub ap_ssid: &'static str,
    
    /// Access point password
    pub ap_password: &'static str,
    
    /// DHCP client enabled
    pub dhcp_enabled: bool,
    
    /// Static IP configuration (if DHCP disabled)
    pub static_ip: Option<StaticIpConfig>,
}

/// Static IP configuration
#[derive(Debug, Clone)]
pub struct StaticIpConfig {
    pub ip: [u8; 4],
    pub subnet: [u8; 4],
    pub gateway: [u8; 4],
    pub dns: [u8; 4],
}

/// Application behavior configuration
#[derive(Debug, Clone)]
pub struct AppConfig {
    /// Enable debug logging
    pub debug_enabled: bool,
    
    /// Main loop interval in milliseconds
    pub loop_interval_ms: u64,
    
    /// Maximum number of valves supported
    pub max_valves: usize,
    
    /// Default valve timeout in seconds
    pub valve_timeout_secs: u32,
    
    /// Enable automatic valve safety shutoff
    pub safety_shutoff_enabled: bool,
    
    /// Task stack sizes
    pub task_stack_size: usize,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            hardware: HardwareConfig::default(),
            network: NetworkConfig::default(),
            app: AppConfig::default(),
            
            // Legacy fields for backward compatibility
            wifi_enabled: true,
            debug_enabled: true,
            loop_interval_ms: 1000,
        }
    }
}

impl Default for HardwareConfig {
    fn default() -> Self {
        Self {
            cpu_clock: CpuClock::max(),
            heap_size: 64 * 1024, // 64KB
            watchdog_enabled: true,
            rng_seed: None, // Use hardware random
        }
    }
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            wifi_enabled: true,
            wifi_ssid: None,
            wifi_password: None,
            ap_mode: false,
            ap_ssid: "ESP32-Manifold",
            ap_password: "manifold123",
            dhcp_enabled: true,
            static_ip: None,
        }
    }
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            debug_enabled: true,
            loop_interval_ms: 1000,
            max_valves: 16,
            valve_timeout_secs: 300, // 5 minutes
            safety_shutoff_enabled: true,
            task_stack_size: 4096,
        }
    }
}

impl Config {
    /// Create a new configuration with production defaults
    pub fn production() -> Self {
        let mut config = Self::default();
        config.app.debug_enabled = false;
        config.hardware.watchdog_enabled = true;
        config.app.safety_shutoff_enabled = true;
        config
    }

    /// Create a new configuration for development
    pub fn development() -> Self {
        let mut config = Self::default();
        config.app.debug_enabled = true;
        config.hardware.watchdog_enabled = false;
        config.app.loop_interval_ms = 500; // Faster loop for development
        config
    }

    /// Create a configuration for testing
    pub fn testing() -> Self {
        let mut config = Self::default();
        config.network.wifi_enabled = false;
        config.app.debug_enabled = true;
        config.app.loop_interval_ms = 100; // Very fast for tests
        config.hardware.heap_size = 32 * 1024; // Smaller heap for tests
        config
    }

    /// Validate the configuration
    pub fn validate(&self) -> Result<(), &'static str> {
        if self.hardware.heap_size < 16 * 1024 {
            return Err("Heap size too small (minimum 16KB)");
        }
        
        if self.app.loop_interval_ms == 0 {
            return Err("Loop interval cannot be zero");
        }
        
        if self.app.max_valves == 0 {
            return Err("Maximum valves must be greater than zero");
        }
        
        if self.network.wifi_enabled && self.network.wifi_ssid.is_none() && !self.network.ap_mode {
            return Err("WiFi enabled but no SSID provided and AP mode disabled");
        }
        
        Ok(())
    }

    /// Update legacy fields from structured config
    pub fn sync_legacy_fields(&mut self) {
        self.wifi_enabled = self.network.wifi_enabled;
        self.debug_enabled = self.app.debug_enabled;
        self.loop_interval_ms = self.app.loop_interval_ms;
    }
}

/// Builder for creating custom configurations
pub struct ConfigBuilder {
    config: Config,
}

impl ConfigBuilder {
    pub fn new() -> Self {
        Self {
            config: Config::default(),
        }
    }

    pub fn hardware(mut self, hardware: HardwareConfig) -> Self {
        self.config.hardware = hardware;
        self
    }

    pub fn network(mut self, network: NetworkConfig) -> Self {
        self.config.network = network;
        self
    }

    pub fn app(mut self, app: AppConfig) -> Self {
        self.config.app = app;
        self
    }

    pub fn build(mut self) -> Result<Config, &'static str> {
        self.config.sync_legacy_fields();
        self.config.validate()?;
        Ok(self.config)
    }
}

impl Default for ConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}
