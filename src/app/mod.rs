//! # Application Module
//!
//! This module contains the main application structure and configuration.
//! It provides a clean separation of concerns and follows the builder pattern
//! for flexible initialization.

pub mod config;
pub mod error;
pub mod tasks;

use defmt::info;
use embassy_executor::Spawner;
use embassy_time::{Duration, Timer};

use crate::hardware::Hardware;
use crate::manifold::Manifold;
use crate::network::Network;

pub use config::Config;
pub use error::{AppError, Result};
pub use tasks::{TaskConfig, TaskManager};

/// Main application struct that orchestrates all components
pub struct App {
    config: Config,
    hardware: Option<Hardware>,
    network: Option<Network>,
    manifold: Option<Manifold>,
    task_manager: Option<TaskManager>,
    spawner: Option<Spawner>,
}

impl App {
    /// Create a new App with default configuration
    pub fn new() -> Self {
        Self {
            config: Config::default(),
            hardware: None,
            network: None,
            manifold: None,
            task_manager: None,
            spawner: None,
        }
    }

    /// Create a new App builder
    pub fn builder() -> AppBuilder {
        AppBuilder::new()
    }

    /// Initialize the application with the given spawner
    pub async fn initialize(&mut self, spawner: Spawner) -> Result<()> {
        info!("Initializing application...");

        self.spawner = Some(spawner);

        // Initialize RTT for debugging first
        Hardware::init_rtt()?;

        // Initialize hardware
        let hardware = Hardware::new(&self.config).await?;
        self.hardware = Some(hardware);

        // Initialize network if enabled
        if self.config.wifi_enabled {
            let network = Network::new(&self.config, self.hardware.as_ref().unwrap()).await?;
            self.network = Some(network);
        }

        // Initialize manifold
        let manifold = Manifold::default();
        self.manifold = Some(manifold);

        // Initialize task manager
        let mut task_manager = TaskManager::new(self.config.app.max_valves as u32);
        task_manager.initialize(spawner);
        self.task_manager = Some(task_manager);

        info!("Application initialized successfully!");
        Ok(())
    }

    /// Run the main application loop
    pub async fn run(&mut self) -> Result<()> {
        info!("Starting application main loop...");

        // Spawn background tasks if needed
        self.spawn_background_tasks().await?;

        // Main application loop
        loop {
            self.tick().await?;
            Timer::after(Duration::from_millis(self.config.loop_interval_ms)).await;
        }
    }

    /// Single iteration of the main loop
    async fn tick(&mut self) -> Result<()> {
        if self.config.debug_enabled {
            info!("Application tick - Hello world!");
        }

        // Add your application logic here
        // For example, check sensors, update manifold state, etc.

        Ok(())
    }

    /// Spawn background tasks
    async fn spawn_background_tasks(&mut self) -> Result<()> {
        if let Some(task_manager) = &mut self.task_manager {
            let task_config = TaskConfig::default();
            tasks::spawn_all_tasks(task_manager, &task_config).await?;
        }
        Ok(())
    }

    /// Get reference to the manifold
    pub fn manifold(&self) -> Option<&Manifold> {
        self.manifold.as_ref()
    }

    /// Get mutable reference to the manifold
    pub fn manifold_mut(&mut self) -> Option<&mut Manifold> {
        self.manifold.as_mut()
    }

    /// Get reference to the hardware
    pub fn hardware(&self) -> Option<&Hardware> {
        self.hardware.as_ref()
    }

    /// Get reference to the network
    pub fn network(&self) -> Option<&Network> {
        self.network.as_ref()
    }

    /// Shutdown the application gracefully
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down application...");

        // Turn off all valves
        if let Some(manifold) = &mut self.manifold {
            manifold.halt();
        }

        // Shutdown network
        if let Some(network) = &mut self.network {
            network.shutdown().await?;
        }

        info!("Application shutdown complete");
        Ok(())
    }
}

impl Default for App {
    fn default() -> Self {
        Self::new()
    }
}

/// Builder for App configuration
pub struct AppBuilder {
    config: Config,
}

impl AppBuilder {
    pub fn new() -> Self {
        Self {
            config: Config::default(),
        }
    }

    pub fn with_config(mut self, config: Config) -> Self {
        self.config = config;
        self
    }

    pub fn with_wifi_enabled(mut self, enabled: bool) -> Self {
        self.config.wifi_enabled = enabled;
        self
    }

    pub fn with_debug_enabled(mut self, enabled: bool) -> Self {
        self.config.debug_enabled = enabled;
        self
    }

    pub fn with_loop_interval(mut self, interval_ms: u64) -> Self {
        self.config.loop_interval_ms = interval_ms;
        self
    }

    pub fn build(self) -> App {
        App {
            config: self.config,
            hardware: None,
            network: None,
            manifold: None,
            task_manager: None,
            spawner: None,
        }
    }
}

impl Default for AppBuilder {
    fn default() -> Self {
        Self::new()
    }
}
