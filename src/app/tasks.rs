//! # Task Management Module
//!
//! This module provides task management functionality for the Embassy executor.
//! It handles spawning and coordination of background tasks.

use embassy_executor::Spawner;
use embassy_time::{Duration, Timer};
use defmt::{info, warn};

use super::error::{Result, TaskError, AppError};

/// Task manager for coordinating background tasks
pub struct TaskManager {
    spawner: Option<Spawner>,
    tasks_spawned: u32,
    max_tasks: u32,
}

impl TaskManager {
    /// Create a new task manager
    pub fn new(max_tasks: u32) -> Self {
        Self {
            spawner: None,
            tasks_spawned: 0,
            max_tasks,
        }
    }

    /// Initialize the task manager with a spawner
    pub fn initialize(&mut self, spawner: Spawner) {
        self.spawner = Some(spawner);
        info!("Task manager initialized");
    }

    /// Spawn a network monitoring task
    pub fn spawn_network_monitor(&mut self) -> Result<()> {
        if let Some(spawner) = &self.spawner {
            if self.tasks_spawned >= self.max_tasks {
                return Err(AppError::Task(TaskError::SpawnFailed("Max tasks reached")));
            }

            spawner.spawn(network_monitor_task()).map_err(|_| {
                AppError::Task(TaskError::SpawnFailed("Failed to spawn network monitor"))
            })?;

            self.tasks_spawned += 1;
            info!("Network monitor task spawned");
            Ok(())
        } else {
            Err(AppError::Task(TaskError::SpawnFailed("Task manager not initialized")))
        }
    }

    /// Spawn a valve safety monitor task
    pub fn spawn_valve_safety_monitor(&mut self) -> Result<()> {
        if let Some(spawner) = &self.spawner {
            if self.tasks_spawned >= self.max_tasks {
                return Err(AppError::Task(TaskError::SpawnFailed("Max tasks reached")));
            }

            spawner.spawn(valve_safety_monitor_task()).map_err(|_| {
                AppError::Task(TaskError::SpawnFailed("Failed to spawn valve safety monitor"))
            })?;

            self.tasks_spawned += 1;
            info!("Valve safety monitor task spawned");
            Ok(())
        } else {
            Err(AppError::Task(TaskError::SpawnFailed("Task manager not initialized")))
        }
    }

    /// Spawn a heartbeat task
    pub fn spawn_heartbeat(&mut self, interval_secs: u64) -> Result<()> {
        if let Some(spawner) = &self.spawner {
            if self.tasks_spawned >= self.max_tasks {
                return Err(AppError::Task(TaskError::SpawnFailed("Max tasks reached")));
            }

            spawner.spawn(heartbeat_task(interval_secs)).map_err(|_| {
                AppError::Task(TaskError::SpawnFailed("Failed to spawn heartbeat"))
            })?;

            self.tasks_spawned += 1;
            info!("Heartbeat task spawned with {}s interval", interval_secs);
            Ok(())
        } else {
            Err(AppError::Task(TaskError::SpawnFailed("Task manager not initialized")))
        }
    }

    /// Get the number of spawned tasks
    pub fn task_count(&self) -> u32 {
        self.tasks_spawned
    }

    /// Check if task manager is initialized
    pub fn is_initialized(&self) -> bool {
        self.spawner.is_some()
    }
}

/// Network monitoring background task
#[embassy_executor::task]
async fn network_monitor_task() {
    info!("Network monitor task started");
    
    loop {
        // TODO: Implement network monitoring logic
        // - Check WiFi connection status
        // - Monitor network traffic
        // - Handle reconnection if needed
        
        Timer::after(Duration::from_secs(30)).await;
    }
}

/// Valve safety monitoring background task
#[embassy_executor::task]
async fn valve_safety_monitor_task() {
    info!("Valve safety monitor task started");
    
    loop {
        // TODO: Implement valve safety monitoring
        // - Check for valve timeouts
        // - Monitor for safety interlocks
        // - Automatic shutoff if needed
        
        Timer::after(Duration::from_secs(5)).await;
    }
}

/// Heartbeat task for system health monitoring
#[embassy_executor::task]
async fn heartbeat_task(interval_secs: u64) {
    info!("Heartbeat task started with {}s interval", interval_secs);
    
    let mut counter = 0u32;
    
    loop {
        counter = counter.wrapping_add(1);
        info!("Heartbeat #{}", counter);
        
        // TODO: Add system health checks
        // - Memory usage
        // - Task status
        // - Hardware status
        
        Timer::after(Duration::from_secs(interval_secs)).await;
    }
}

/// Task configuration structure
#[derive(Debug, Clone)]
pub struct TaskConfig {
    /// Enable network monitoring task
    pub network_monitor_enabled: bool,
    
    /// Enable valve safety monitoring task
    pub valve_safety_enabled: bool,
    
    /// Enable heartbeat task
    pub heartbeat_enabled: bool,
    
    /// Heartbeat interval in seconds
    pub heartbeat_interval_secs: u64,
    
    /// Maximum number of background tasks
    pub max_background_tasks: u32,
}

impl Default for TaskConfig {
    fn default() -> Self {
        Self {
            network_monitor_enabled: true,
            valve_safety_enabled: true,
            heartbeat_enabled: true,
            heartbeat_interval_secs: 60,
            max_background_tasks: 8,
        }
    }
}

impl TaskConfig {
    /// Create a minimal task configuration
    pub fn minimal() -> Self {
        Self {
            network_monitor_enabled: false,
            valve_safety_enabled: true,
            heartbeat_enabled: false,
            heartbeat_interval_secs: 300,
            max_background_tasks: 2,
        }
    }

    /// Create a full-featured task configuration
    pub fn full() -> Self {
        Self {
            network_monitor_enabled: true,
            valve_safety_enabled: true,
            heartbeat_enabled: true,
            heartbeat_interval_secs: 30,
            max_background_tasks: 16,
        }
    }
}

/// Initialize and spawn all configured background tasks
pub async fn spawn_all_tasks(
    task_manager: &mut TaskManager,
    config: &TaskConfig,
) -> Result<()> {
    info!("Spawning background tasks...");

    if config.network_monitor_enabled {
        task_manager.spawn_network_monitor()?;
    }

    if config.valve_safety_enabled {
        task_manager.spawn_valve_safety_monitor()?;
    }

    if config.heartbeat_enabled {
        task_manager.spawn_heartbeat(config.heartbeat_interval_secs)?;
    }

    info!("All background tasks spawned successfully");
    Ok(())
}
