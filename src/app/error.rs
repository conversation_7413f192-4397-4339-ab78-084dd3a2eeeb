//! # Error Handling Module
//!
//! This module provides comprehensive error handling for the application.
//! It defines custom error types and provides conversion from various
//! underlying error types.

use defmt::Format;

/// Application result type
pub type Result<T> = core::result::Result<T, AppError>;

/// Main application error type
#[derive(Debug, Format)]
pub enum AppError {
    /// Hardware initialization errors
    Hardware(HardwareError),
    
    /// Network-related errors
    Network(NetworkError),
    
    /// Configuration errors
    Config(&'static str),
    
    /// Manifold control errors
    Manifold(ManifoldError),
    
    /// Task management errors
    Task(TaskError),
    
    /// Generic initialization error
    Initialization(&'static str),
    
    /// Runtime errors
    Runtime(&'static str),
}

/// Hardware-specific errors
#[derive(Debug, Format)]
pub enum HardwareError {
    /// Failed to initialize peripherals
    PeripheralInit(&'static str),
    
    /// Timer configuration failed
    TimerConfig(&'static str),
    
    /// RNG initialization failed
    RngInit(&'static str),
    
    /// Memory allocation failed
    MemoryAllocation(&'static str),
    
    /// GPIO configuration failed
    GpioConfig(&'static str),
}

/// Network-specific errors
#[derive(Debug, Format)]
pub enum NetworkError {
    /// WiFi initialization failed
    WifiInit(&'static str),
    
    /// WiFi connection failed
    WifiConnection(&'static str),
    
    /// Network configuration error
    Configuration(&'static str),
    
    /// DHCP failed
    Dhcp(&'static str),
    
    /// DNS resolution failed
    Dns(&'static str),
}

/// Manifold control errors
#[derive(Debug, Format)]
pub enum ManifoldError {
    /// Valve not found
    ValveNotFound(&'static str),
    
    /// Valve operation failed
    ValveOperation(&'static str),
    
    /// Safety interlock triggered
    SafetyInterlock(&'static str),
    
    /// Maximum valves exceeded
    MaxValvesExceeded,
}

/// Task management errors
#[derive(Debug, Format)]
pub enum TaskError {
    /// Failed to spawn task
    SpawnFailed(&'static str),
    
    /// Task communication failed
    Communication(&'static str),
    
    /// Task timeout
    Timeout(&'static str),
}

impl AppError {
    /// Check if the error is recoverable
    pub fn is_recoverable(&self) -> bool {
        match self {
            AppError::Hardware(HardwareError::MemoryAllocation(_)) => false,
            AppError::Hardware(HardwareError::PeripheralInit(_)) => false,
            AppError::Config(_) => false,
            AppError::Network(_) => true, // Network errors are usually recoverable
            AppError::Manifold(ManifoldError::SafetyInterlock(_)) => false,
            AppError::Manifold(_) => true,
            AppError::Task(_) => true,
            AppError::Initialization(_) => false,
            AppError::Runtime(_) => true,
        }
    }

    /// Get error severity level
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            AppError::Hardware(HardwareError::MemoryAllocation(_)) => ErrorSeverity::Critical,
            AppError::Hardware(HardwareError::PeripheralInit(_)) => ErrorSeverity::Critical,
            AppError::Config(_) => ErrorSeverity::Critical,
            AppError::Manifold(ManifoldError::SafetyInterlock(_)) => ErrorSeverity::Critical,
            AppError::Initialization(_) => ErrorSeverity::High,
            AppError::Network(_) => ErrorSeverity::Medium,
            AppError::Manifold(_) => ErrorSeverity::Medium,
            AppError::Task(_) => ErrorSeverity::Low,
            AppError::Runtime(_) => ErrorSeverity::Medium,
        }
    }

    /// Get a user-friendly error message
    pub fn user_message(&self) -> &'static str {
        match self {
            AppError::Hardware(_) => "Hardware initialization failed",
            AppError::Network(_) => "Network connection failed",
            AppError::Config(_) => "Configuration error",
            AppError::Manifold(_) => "Valve control error",
            AppError::Task(_) => "Task management error",
            AppError::Initialization(_) => "Application initialization failed",
            AppError::Runtime(_) => "Runtime error occurred",
        }
    }
}

/// Error severity levels
#[derive(Debug, Format, PartialEq, Eq, PartialOrd, Ord)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

// Conversion implementations for common error types
impl From<HardwareError> for AppError {
    fn from(err: HardwareError) -> Self {
        AppError::Hardware(err)
    }
}

impl From<NetworkError> for AppError {
    fn from(err: NetworkError) -> Self {
        AppError::Network(err)
    }
}

impl From<ManifoldError> for AppError {
    fn from(err: ManifoldError) -> Self {
        AppError::Manifold(err)
    }
}

impl From<TaskError> for AppError {
    fn from(err: TaskError) -> Self {
        AppError::Task(err)
    }
}

/// Error recovery strategies
pub enum RecoveryStrategy {
    /// Retry the operation
    Retry { max_attempts: u32, delay_ms: u64 },
    
    /// Fallback to alternative method
    Fallback,
    
    /// Continue with degraded functionality
    Degrade,
    
    /// Shutdown gracefully
    Shutdown,
    
    /// Panic (for critical errors)
    Panic,
}

impl AppError {
    /// Get the recommended recovery strategy for this error
    pub fn recovery_strategy(&self) -> RecoveryStrategy {
        match self {
            AppError::Hardware(HardwareError::MemoryAllocation(_)) => RecoveryStrategy::Panic,
            AppError::Hardware(_) => RecoveryStrategy::Shutdown,
            AppError::Config(_) => RecoveryStrategy::Shutdown,
            AppError::Manifold(ManifoldError::SafetyInterlock(_)) => RecoveryStrategy::Shutdown,
            AppError::Network(_) => RecoveryStrategy::Retry { max_attempts: 3, delay_ms: 5000 },
            AppError::Manifold(_) => RecoveryStrategy::Retry { max_attempts: 2, delay_ms: 1000 },
            AppError::Task(_) => RecoveryStrategy::Fallback,
            AppError::Initialization(_) => RecoveryStrategy::Shutdown,
            AppError::Runtime(_) => RecoveryStrategy::Degrade,
        }
    }
}

/// Macro for creating hardware errors
#[macro_export]
macro_rules! hardware_error {
    ($msg:expr) => {
        $crate::app::error::AppError::Hardware(
            $crate::app::error::HardwareError::PeripheralInit($msg)
        )
    };
}

/// Macro for creating network errors
#[macro_export]
macro_rules! network_error {
    ($msg:expr) => {
        $crate::app::error::AppError::Network(
            $crate::app::error::NetworkError::Configuration($msg)
        )
    };
}
