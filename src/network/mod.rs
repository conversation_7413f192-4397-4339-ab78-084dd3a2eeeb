//! # Network Management Module
//!
//! This module provides network functionality including WiFi management.

pub mod wifi;

use defmt::info;

use crate::app::config::Config;
use crate::app::error::{AppError, NetworkError, Result};
use crate::hardware::Hardware;

pub use wifi::{WiFiManager, WiFiStatus};

/// Main network manager
pub struct Network {
    wifi: Option<WiFiManager>,
    config: NetworkConfig,
}

/// Network configuration
#[derive(Debug, Clone)]
pub struct NetworkConfig {
    pub wifi_enabled: bool,
    pub auto_reconnect: bool,
    pub connection_timeout_secs: u32,
    pub max_retry_attempts: u32,
}

impl Default for NetworkConfig {
    fn default() -> Self {
        Self {
            wifi_enabled: true,
            auto_reconnect: true,
            connection_timeout_secs: 30,
            max_retry_attempts: 3,
        }
    }
}

impl Network {
    /// Create a new network manager
    pub async fn new(config: &Config, hardware: &Hardware) -> Result<Self> {
        info!("Initializing network...");

        let network_config = NetworkConfig {
            wifi_enabled: config.network.wifi_enabled,
            auto_reconnect: true,
            connection_timeout_secs: 30,
            max_retry_attempts: 3,
        };

        let mut network = Self {
            wifi: None,
            config: network_config,
        };

        // Initialize WiFi if enabled
        if config.network.wifi_enabled {
            let wifi_manager = WiFiManager::new(config, hardware).await?;
            network.wifi = Some(wifi_manager);
            info!("WiFi manager initialized");
        }

        info!("Network initialization complete");
        Ok(network)
    }

    /// Get WiFi manager reference
    pub fn wifi(&self) -> Option<&WiFiManager> {
        self.wifi.as_ref()
    }

    /// Get mutable WiFi manager reference
    pub fn wifi_mut(&mut self) -> Option<&mut WiFiManager> {
        self.wifi.as_mut()
    }

    /// Connect to WiFi network
    pub async fn connect_wifi(&mut self, ssid: &str, password: &str) -> Result<()> {
        if let Some(wifi) = &mut self.wifi {
            wifi.connect(ssid, password).await
        } else {
            Err(AppError::Network(NetworkError::Configuration(
                "WiFi not enabled",
            )))
        }
    }

    /// Disconnect from WiFi
    pub async fn disconnect_wifi(&mut self) -> Result<()> {
        if let Some(wifi) = &mut self.wifi {
            wifi.disconnect().await
        } else {
            Ok(()) // No WiFi to disconnect
        }
    }

    /// Get network status
    pub fn status(&self) -> NetworkStatus {
        NetworkStatus {
            wifi_enabled: self.config.wifi_enabled,
            wifi_status: self.wifi.as_ref().map(|w| w.status()),
            auto_reconnect: self.config.auto_reconnect,
        }
    }

    /// Start access point mode
    pub async fn start_access_point(&mut self, ssid: &str, password: &str) -> Result<()> {
        if let Some(wifi) = &mut self.wifi {
            wifi.start_access_point(ssid, password).await
        } else {
            Err(AppError::Network(NetworkError::Configuration(
                "WiFi not enabled",
            )))
        }
    }

    /// Stop access point mode
    pub async fn stop_access_point(&mut self) -> Result<()> {
        if let Some(wifi) = &mut self.wifi {
            wifi.stop_access_point().await
        } else {
            Ok(()) // No AP to stop
        }
    }

    /// Scan for available WiFi networks
    pub async fn scan_networks(&mut self) -> Result<heapless::Vec<NetworkInfo, 16>> {
        if let Some(wifi) = &mut self.wifi {
            wifi.scan_networks().await
        } else {
            Err(AppError::Network(NetworkError::Configuration(
                "WiFi not enabled",
            )))
        }
    }

    /// Perform network self-test
    pub async fn self_test(&mut self) -> Result<NetworkSelfTestResult> {
        info!("Performing network self-test...");

        let mut result = NetworkSelfTestResult::default();

        // Test WiFi if available
        if let Some(wifi) = &mut self.wifi {
            result.wifi_init = wifi.self_test().await;
        } else {
            result.wifi_init = true; // No WiFi to test
        }

        // TODO: Add more network tests
        // - DNS resolution test
        // - Internet connectivity test
        // - Local network connectivity test

        info!("Network self-test complete: {:?}", result);
        Ok(result)
    }

    /// Shutdown network gracefully
    pub async fn shutdown(&mut self) -> Result<()> {
        info!("Shutting down network...");

        // Disconnect WiFi
        if let Some(wifi) = &mut self.wifi {
            wifi.shutdown().await?;
        }

        info!("Network shutdown complete");
        Ok(())
    }

    /// Enable auto-reconnect
    pub fn enable_auto_reconnect(&mut self) {
        self.config.auto_reconnect = true;
        if let Some(wifi) = &mut self.wifi {
            wifi.enable_auto_reconnect();
        }
    }

    /// Disable auto-reconnect
    pub fn disable_auto_reconnect(&mut self) {
        self.config.auto_reconnect = false;
        if let Some(wifi) = &mut self.wifi {
            wifi.disable_auto_reconnect();
        }
    }
}

/// Network status information
#[derive(Debug)]
pub struct NetworkStatus {
    pub wifi_enabled: bool,
    pub wifi_status: Option<WiFiStatus>,
    pub auto_reconnect: bool,
}

impl NetworkStatus {
    /// Check if network is connected
    pub fn is_connected(&self) -> bool {
        self.wifi_status
            .as_ref()
            .map(|s| s.is_connected())
            .unwrap_or(false)
    }

    /// Get connection type
    pub fn connection_type(&self) -> &'static str {
        if self.wifi_enabled {
            if self.is_connected() {
                "WiFi"
            } else {
                "WiFi (disconnected)"
            }
        } else {
            "None"
        }
    }
}

/// Network information for scanned networks
#[derive(Debug, Clone)]
pub struct NetworkInfo {
    pub ssid: heapless::String<32>,
    pub signal_strength: i8,
    pub security: SecurityType,
    pub channel: u8,
}

/// WiFi security types
#[derive(Debug, Clone)]
pub enum SecurityType {
    Open,
    WEP,
    WPA,
    WPA2,
    WPA3,
    Unknown,
}

/// Network self-test results
#[derive(Debug, Default, defmt::Format)]
pub struct NetworkSelfTestResult {
    pub wifi_init: bool,
    pub dns_resolution: bool,
    pub internet_connectivity: bool,
    pub local_connectivity: bool,
}

impl NetworkSelfTestResult {
    /// Check if all tests passed
    pub fn all_passed(&self) -> bool {
        self.wifi_init
            && self.dns_resolution
            && self.internet_connectivity
            && self.local_connectivity
    }

    /// Get failed test count
    pub fn failed_count(&self) -> u8 {
        let mut count = 0;

        if !self.wifi_init {
            count += 1;
        }
        if !self.dns_resolution {
            count += 1;
        }
        if !self.internet_connectivity {
            count += 1;
        }
        if !self.local_connectivity {
            count += 1;
        }

        count
    }
}
